import { LandingPageVariant } from '@/types';

export const landingPageVariants: LandingPageVariant[] = [
  {
    id: 'founder-focused',
    name: 'Founder Focused',
    slug: 'founder-focused',
    hero: {
      headline: "Got an App Idea? We'll Build It. You Pay <20%. We Take Equity.",
      subheadline: "No freelancers. No fluff. Just your product — launched. Partner with fullstack founders who know how to build and scale.",
      ctaText: "Pitch Your Idea →",
      backgroundType: 'video',
      backgroundConfig: {
        gradient: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50',
        videoUrl: '/videos/group-of-people.mp4'
      }
    },
    howItWorks: {
      title: "How It Works",
      steps: [
        {
          icon: '💡',
          iconType: 'emoji',
          title: 'Pitch us your idea',
          description: 'Share your vision in a quick 30-minute call. We evaluate feasibility, market potential, and technical requirements.'
        },
        {
          icon: '🛠️',
          iconType: 'emoji',
          title: 'We build MVP fast & lean',
          description: 'Our experienced team develops your MVP using proven frameworks and best practices. No bloated features, just what you need to launch.'
        },
        {
          icon: '📈',
          iconType: 'emoji',
          title: 'You keep 80% — we grow together',
          description: 'Launch with minimal upfront cost. We take 20% equity and become your long-term technical partners in scaling your business.'
        }
      ]
    },
    projects: {
      title: 'Sample Projects',
      subtitle: 'See what we\'ve built for ambitious founders like you',
      projects: [
        {
          id: 'neuronest',
          name: 'NeuroNest',
          category: 'Wellness',
          description: 'AI-powered CBT journaling and therapy planner for ADHD & neurodivergent adults.',
          imageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-3.png',
          mobileImageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-3.png',
          webImageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-4.png',
          tags: ['React Native', 'GPT API', 'MongoDB', 'Notion API'],
          stats: {
            equity: '12%',
            stage: 'Beta Testing',
            founder: 'Psychology Graduate'
          },
          story: 'Bootstrapped psychology grad. We built the MVP for 12% equity.',
          type: 'mobile'
        },
        {
          id: 'tabletide',
          name: 'TableTide',
          category: 'FoodTech',
          description: 'Real-time dish-level reviews for restaurants, powered by user-generated content and AI photo recognition.',
          imageUrl: '/projects/project-mockups/TableTide (MOBILE)/Unknown-5.png',
          mobileImageUrl: '/projects/project-mockups/TableTide (MOBILE)/Unknown-5.png',
          webImageUrl: '/projects/project-mockups/TableTide (MOBILE)/Unknown-6.png',
          tags: ['Flutter', 'Firebase', 'OpenAI', 'Google Maps API'],
          stats: {
            equity: '18%',
            stage: 'MVP Complete',
            founder: 'Restaurant Industry Veteran'
          },
          story: 'No capital, just a dream to fix Yelp. We built this for 18% equity.',
          type: 'mobile'
        },
        {
          id: 'artnest',
          name: 'ArtNest',
          category: 'Creator Platform',
          description: 'Digital art marketplace where illustrators sell subscriptions instead of one-off NFTs. Fans unlock works in a "season pass" format.',
          imageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-7.png',
          mobileImageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-7.png',
          webImageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-8.png',
          tags: ['Next.js', 'Web3.js', 'Supabase', 'Stripe'],
          stats: {
            equity: '10%',
            stage: 'Live Marketplace',
            founder: 'Digital Artist'
          },
          story: 'Talented broke artist. We brought their vision to life for a 10% stake.',
          type: 'web'
        },
        {
          id: 'splitiq',
          name: 'SplitIQ',
          category: 'FinTech',
          description: 'Advanced group finance app that uses AI to resolve IOUs, track shared subscriptions, and auto-reconcile PayPal, Venmo, and bank transfers.',
          imageUrl: '/projects/project-mockups/Split IQ (mobile)/Unknown-10.png',
          mobileImageUrl: '/projects/project-mockups/Split IQ (mobile)/Unknown-10.png',
          webImageUrl: '/projects/project-mockups/Split IQ(web)/Unknown-16.png',
          tags: ['React Native', 'Plaid API', 'GPT', 'PostgreSQL'],
          stats: {
            equity: '14%',
            stage: 'Beta Launch',
            founder: 'College Students'
          },
          story: 'Founders had the idea in college but no dev. We took equity and delivered.',
          type: 'both'
        },
        {
          id: 'triptag',
          name: 'TripTag',
          category: 'Travel',
          description: 'Shared trip itineraries with group voting, expense tracking, and real-time route adjustments.',
          imageUrl: '/projects/project-mockups/TripTag(mobile)/Unknown-18.png',
          mobileImageUrl: '/projects/project-mockups/TripTag(mobile)/Unknown-18.png',
          webImageUrl: '/projects/project-mockups/Trip Tag (web)/Unknown-20.png',
          tags: ['React', 'Node.js', 'Google Maps API', 'Socket.io'],
          stats: {
            equity: '16%',
            stage: 'MVP Testing',
            founder: 'Travel Influencer from Peru'
          },
          story: 'Travel influencer from Peru came to us with the idea. We joined as tech co-founders.',
          type: 'both'
        },
        {
          id: 'petloop',
          name: 'PetLoop',
          category: 'PetTech',
          description: 'All-in-one pet wellness tracker: vet appointments, food tracking, vaccinations, AI behavior tips.',
          imageUrl: '/projects/project-mockups/PetLoop (mobile)/Unknown-22.png',
          mobileImageUrl: '/projects/project-mockups/PetLoop (mobile)/Unknown-22.png',
          webImageUrl: '/projects/project-mockups/PetLoop (mobile)/Unknown-23.png',
          tags: ['Flutter', 'Firebase', 'GPT', 'Calendar API'],
          stats: {
            equity: '15%',
            stage: 'Live App',
            founder: 'Vet Nurse'
          },
          story: 'Vet nurse with no coding skills—now a co-founder with us at 15% equity.',
          type: 'mobile'
        }
      ]
    },
    whyEquity: {
      title: 'Why Equity Builds?',
      subtitle: 'We\'re not just developers — we\'re fullstack founders who know how to launch, fast.',
      points: [
        'Aligned incentives: We only succeed when you succeed',
        'Experienced team: 50+ successful launches under our belt',
        'Full-stack expertise: From idea to scale, we handle everything',
        'Ongoing support: We\'re your technical co-founders for life',
        'Proven process: Battle-tested methodology for rapid MVP development',
        'Network access: Connect with our portfolio of successful founders'
      ],
      credibilityStatement: 'We\'re fullstack founders. We know how to launch, fast.'
    },
    faq: {
      title: 'Frequently Asked Questions',
      faqs: [
        {
          question: 'Why do you take equity?',
          answer: 'Taking equity aligns our interests with yours. We\'re not just building your product and walking away — we\'re invested in your long-term success. This model allows us to offer world-class development at a fraction of the typical cost while ensuring we\'re motivated to build something that actually works and grows.'
        },
        {
          question: 'What kind of ideas qualify?',
          answer: 'We work with tech-enabled businesses that have clear monetization potential. This includes SaaS platforms, mobile apps, marketplaces, fintech solutions, and more. We evaluate each idea based on market size, technical feasibility, and the founder\'s commitment to the vision.'
        },
        {
          question: 'How fast can we launch?',
          answer: 'Most MVPs are launched within 8-12 weeks. Our lean development process focuses on core features that validate your business model. We can move faster because we\'re not building everything — just what you need to test your market and start generating revenue.'
        },
        {
          question: 'What happens after the MVP?',
          answer: 'We become your ongoing technical partners. As equity holders, we\'re invested in scaling your product, adding features, and supporting your growth. You get a dedicated technical team without the overhead of hiring full-time developers.'
        },
        {
          question: 'How much equity do you typically take?',
          answer: 'We typically take 15-25% equity depending on the scope and complexity of the project. This is significantly less than what you\'d pay for equivalent development work upfront, and it ensures we\'re aligned with your success.'
        }
      ]
    },
    cta: {
      title: 'Ready to Build Your MVP?',
      subtitle: 'Join 100+ founders who chose the equity partnership model',
      formTitle: 'Tell us your idea in 2 mins'
    },
    theme: {
      primaryColor: '#3B82F6',
      secondaryColor: '#8B5CF6',
      accentColor: '#F59E0B',
      backgroundColor: '#FFFFFF',
      textColor: '#1F2937',
      fontFamily: 'Inter'
    }
  },
  {
    id: 'bold-disruptor',
    name: 'Bold Disruptor',
    slug: 'bold-disruptor',
    hero: {
      headline: "Turn Your Startup Dream Into Reality — Pay Almost Nothing Upfront",
      subheadline: "We build world-class MVPs for <20% upfront cost. You keep 80% equity. We bet on your success because we believe in building the future together.",
      ctaText: "Start Building Now →",
      backgroundType: 'video',
      backgroundConfig: {
        gradient: 'bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900',
        videoUrl: '/videos/startup-man.mp4'
      }
    },
    howItWorks: {
      title: "Three Steps to Launch",
      steps: [
        {
          icon: '🚀',
          iconType: 'emoji',
          title: 'Share Your Vision',
          description: 'Book a strategy session where we dive deep into your idea, validate the market opportunity, and create a launch roadmap.'
        },
        {
          icon: '⚡',
          iconType: 'emoji',
          title: 'We Build Lightning Fast',
          description: 'Our elite development team uses cutting-edge tools and frameworks to build your MVP in weeks, not months.'
        },
        {
          icon: '🎯',
          iconType: 'emoji',
          title: 'Launch & Scale Together',
          description: 'Go to market with a product that works. We stay as your technical partners, scaling as you grow and succeed.'
        }
      ]
    },
    projects: {
      title: 'Success Stories',
      subtitle: 'Real startups. Real results. Real partnerships.',
      projects: [
        {
          id: 'splitiq-bold',
          name: 'SplitIQ',
          category: 'FinTech',
          description: 'Advanced group finance app that uses AI to resolve IOUs, track shared subscriptions, and auto-reconcile PayPal, Venmo, and bank transfers.',
          imageUrl: '/projects/project-mockups/Split IQ(web)/Unknown-17.png',
          mobileImageUrl: '/projects/project-mockups/Split IQ (mobile)/Unknown-11.png',
          webImageUrl: '/projects/project-mockups/Split IQ(web)/Unknown-17.png',
          tags: ['React Native', 'Plaid API', 'GPT', 'PostgreSQL'],
          stats: {
            equity: '14%',
            stage: 'Beta Launch',
            founder: 'College Students'
          },
          story: 'Founders had the idea in college but no dev. We took equity and delivered.',
          type: 'both'
        },
        {
          id: 'artnest-bold',
          name: 'ArtNest',
          category: 'Creator Platform',
          description: 'Digital art marketplace where illustrators sell subscriptions instead of one-off NFTs. Fans unlock works in a "season pass" format.',
          imageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-9.png',
          mobileImageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-8.png',
          webImageUrl: '/projects/project-mockups/ArtNest(Web)/Unknown-9.png',
          tags: ['Next.js', 'Web3.js', 'Supabase', 'Stripe'],
          stats: {
            equity: '10%',
            stage: 'Live Marketplace',
            founder: 'Digital Artist'
          },
          story: 'Talented broke artist. We brought their vision to life for a 10% stake.',
          type: 'web'
        },
        {
          id: 'triptag-bold',
          name: 'TripTag',
          category: 'Travel',
          description: 'Shared trip itineraries with group voting, expense tracking, and real-time route adjustments.',
          imageUrl: '/projects/project-mockups/Trip Tag (web)/Unknown-21.png',
          mobileImageUrl: '/projects/project-mockups/TripTag(mobile)/Unknown-19.png',
          webImageUrl: '/projects/project-mockups/Trip Tag (web)/Unknown-21.png',
          tags: ['React', 'Node.js', 'Google Maps API', 'Socket.io'],
          stats: {
            equity: '16%',
            stage: 'MVP Testing',
            founder: 'Travel Influencer from Peru'
          },
          story: 'Travel influencer from Peru came to us with the idea. We joined as tech co-founders.',
          type: 'both'
        },
        {
          id: 'neuronest-bold',
          name: 'NeuroNest',
          category: 'Wellness',
          description: 'AI-powered CBT journaling and therapy planner for ADHD & neurodivergent adults.',
          imageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-4.png',
          mobileImageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-3.png',
          webImageUrl: '/projects/project-mockups/Neuronest (mobile)/Unknown-4.png',
          tags: ['React Native', 'GPT API', 'MongoDB', 'Notion API'],
          stats: {
            equity: '12%',
            stage: 'Beta Testing',
            founder: 'Psychology Graduate'
          },
          story: 'Bootstrapped psychology grad. We built the MVP for 12% equity.',
          type: 'mobile'
        }
      ]
    },
    whyEquity: {
      title: 'Why Partner With Us?',
      subtitle: 'We don\'t just build apps. We build businesses. We build futures.',
      points: [
        'Skin in the game: We only win when you win big',
        'Elite team: Top 1% developers and designers who\'ve built unicorns',
        'Speed to market: Launch in weeks with our proven rapid development process',
        'Ongoing partnership: We\'re your technical co-founders, not just contractors',
        'Network effect: Access to our ecosystem of successful entrepreneurs and investors',
        'Risk mitigation: Minimal upfront investment with maximum potential upside'
      ],
      credibilityStatement: 'We\'ve helped build 3 unicorns and 50+ successful startups. Now it\'s your turn.'
    },
    faq: {
      title: 'Everything You Need to Know',
      faqs: [
        {
          question: 'Why should I give up equity instead of paying cash?',
          answer: 'Because we\'re not just service providers — we\'re partners. With equity, you get world-class development at 80% less upfront cost, plus ongoing technical leadership as you scale. We\'re invested in your success for the long term, not just until the project is done.'
        },
        {
          question: 'What if my idea doesn\'t work out?',
          answer: 'That\'s the beauty of the equity model — your risk is minimized. You pay very little upfront, and if the idea doesn\'t work, you haven\'t lost a fortune in development costs. We share the risk with you because we believe in building sustainable businesses.'
        },
        {
          question: 'How do you choose which projects to work on?',
          answer: 'We\'re selective. We look for ambitious founders with clear vision, large market opportunities, and ideas that can scale. We evaluate technical feasibility, market potential, and most importantly, the founder\'s commitment to success.'
        },
        {
          question: 'What\'s your track record?',
          answer: 'We\'ve been part of building 3 unicorn companies and over 50 successful startups. Our portfolio companies have raised over $500M in funding and generated billions in revenue. We know what it takes to build and scale successful tech companies.'
        },
        {
          question: 'How involved are you after launch?',
          answer: 'Very involved. As equity partners, we provide ongoing technical leadership, help with hiring, scaling infrastructure, and strategic decisions. Think of us as your technical co-founders who are invested in your long-term success.'
        }
      ]
    },
    cta: {
      title: 'Ready to Disrupt Your Industry?',
      subtitle: 'Join the ranks of successful founders who chose partnership over traditional development',
      formTitle: 'Let\'s build the future together'
    },
    theme: {
      primaryColor: '#7C3AED',
      secondaryColor: '#EC4899',
      accentColor: '#F59E0B',
      backgroundColor: '#FFFFFF',
      textColor: '#1F2937',
      fontFamily: 'Inter'
    }
  }
];

export function getLandingPageBySlug(slug: string): LandingPageVariant | undefined {
  return landingPageVariants.find(variant => variant.slug === slug);
}

export function getAllLandingPageSlugs(): string[] {
  return landingPageVariants.map(variant => variant.slug);
}
