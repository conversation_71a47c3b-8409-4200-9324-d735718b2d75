export interface LandingPageVariant {
  id: string;
  name: string;
  slug: string;
  hero: HeroSection;
  howItWorks: HowItWorksSection;
  projects: ProjectsSection;
  whyEquity: WhyEquitySection;
  faq: FAQSection;
  cta: CTASection;
  theme: ThemeConfig;
}

export interface HeroSection {
  headline: string;
  subheadline: string;
  ctaText: string;
  backgroundType: 'gradient' | 'video' | 'image';
  backgroundConfig: {
    gradient?: string;
    videoUrl?: string;
    imageUrl?: string;
  };
}

export interface HowItWorksSection {
  title: string;
  steps: Step[];
}

export interface Step {
  icon: string;
  title: string;
  description: string;
  iconType: 'emoji' | 'lucide' | 'custom';
}

export interface ProjectsSection {
  title: string;
  subtitle: string;
  projects: MockProject[];
}

export interface MockProject {
  id: string;
  name: string;
  category: string;
  description: string;
  imageUrl: string;
  tags: string[];
  stats?: {
    users?: string;
    revenue?: string;
    growth?: string;
  };
}

export interface WhyEquitySection {
  title: string;
  subtitle: string;
  points: string[];
  credibilityStatement: string;
}

export interface FAQSection {
  title: string;
  faqs: FAQ[];
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface CTASection {
  title: string;
  subtitle: string;
  formTitle: string;
}

export interface ThemeConfig {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
}

export interface FormData {
  name: string;
  email: string;
  ideaSummary: string;
  estimatedBudget: string;
  timeline: string;
  phone?: string;
  company?: string;
  additionalInfo?: string;
}

export interface FormSubmissionResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
}

export interface AnimationConfig {
  duration: number;
  delay: number;
  easing: string;
  direction: 'up' | 'down' | 'left' | 'right' | 'fade';
}

export interface ContactInfo {
  email: string;
  phone: string;
  whatsapp: string;
  website: string;
}
