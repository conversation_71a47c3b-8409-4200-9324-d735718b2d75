import { NextRequest, NextResponse } from 'next/server';
import { validateEmailAdvanced } from '@/lib/form-submission';
import { checkRateLimit } from '@/lib/rate-limit';
import { submitToCRM, formatContactFormData, formatScheduleCallData } from '@/lib/crm-integration';

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return 'unknown';
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, message, company, budget, timeline, type, preferredDate, preferredTime, ideaSummary } = body;

    // Get client IP for rate limiting
    const clientIP = getClientIP(request);

    // Check rate limit
    const rateLimitResult = checkRateLimit(clientIP, 3, 30);

    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: `Rate limit exceeded. Please try again in ${rateLimitResult.minutesUntilReset} minutes.`,
          rateLimitExceeded: true,
          minutesUntilReset: rateLimitResult.minutesUntilReset
        },
        { status: 429 }
      );
    }

    // Validate required fields based on form type
    if (type === 'schedule') {
      if (!name || !email || !preferredDate || !preferredTime) {
        return NextResponse.json(
          { success: false, message: 'Missing required fields for schedule call' },
          { status: 400 }
        );
      }
    } else if (type === 'modal') {
      if (!name || !email || !ideaSummary) {
        return NextResponse.json(
          { success: false, message: 'Missing required fields for idea submission' },
          { status: 400 }
        );
      }
    } else {
      if (!name || !email || !message) {
        return NextResponse.json(
          { success: false, message: 'Missing required fields' },
          { status: 400 }
        );
      }
    }

    // Validate email
    const emailValidation = await validateEmailAdvanced(email);
    if (!emailValidation.valid) {
      return NextResponse.json(
        { success: false, message: emailValidation.reason || 'Invalid email address' },
        { status: 400 }
      );
    }

    // Format data for CRM based on form type
    let crmData;
    if (type === 'schedule') {
      crmData = formatScheduleCallData({
        name,
        email,
        phone,
        preferredDate,
        preferredTime,
        message
      });
    } else if (type === 'modal') {
      crmData = formatContactFormData({
        name,
        email,
        phone,
        ideaSummary,
        company,
        budget,
        timeline
      });
    } else {
      crmData = formatContactFormData({
        name,
        email,
        phone,
        message,
        company,
        budget,
        timeline
      });
    }

    // Submit to CRM
    const crmResult = await submitToCRM(crmData);

    if (!crmResult.success) {
      console.error('CRM submission failed:', crmResult.message);
      // Continue with success response even if CRM fails
    }

    // Log the submission for backup
    console.log('Form submission:', {
      type,
      name,
      email,
      phone,
      message,
      ideaSummary,
      company,
      budget,
      timeline,
      preferredDate,
      preferredTime,
      timestamp: new Date().toISOString(),
      crmSubmitted: crmResult.success,
      clientIP,
      rateLimitRemaining: rateLimitResult.remaining
    });

    let successMessage;
    if (type === 'schedule') {
      successMessage = 'Thank you! Your call has been scheduled. We\'ll send you a calendar invite within 24 hours.';
    } else if (type === 'modal') {
      successMessage = 'Thank you for sharing your idea! We\'ll review it and get back to you within 24 hours.';
    } else {
      successMessage = 'Thank you for your submission! We\'ll get back to you within 24 hours.';
    }

    return NextResponse.json({
      success: true,
      message: successMessage,
      rateLimitRemaining: rateLimitResult.remaining,
      data: {
        submissionId: `sub_${Date.now()}`,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Form submission error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Form submission endpoint. Use POST method.' },
    { status: 405 }
  );
}
