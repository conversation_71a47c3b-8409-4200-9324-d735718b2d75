import { Metadata } from 'next';
import { LandingPage } from '@/components/LandingPage';
import { getLandingPageBySlug } from '@/data/landing-pages';
import { notFound } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Build Your MVP for <20% Upfront | He<PERSON>on - Founder Focused',
  description: 'Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity. No freelancers, no fluff - just your product launched fast.',
  keywords: 'MVP development, startup equity, founder partnership, app development, tech startup',
  openGraph: {
    title: 'Build Your MVP for <20% Upfront | Helevon',
    description: 'Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity.',
    type: 'website',
    url: '/founder-focused',
    images: [
      {
        url: '/og-founder-focused.jpg',
        width: 1200,
        height: 630,
        alt: 'He<PERSON>on - Founder Focused MVP Development',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Build Your MVP for <20% Upfront | Helevon',
    description: 'Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity.',
    images: ['/og-founder-focused.jpg'],
  },
};

export default function FounderFocusedPage() {
  const landingPageData = getLandingPageBySlug('founder-focused');
  
  if (!landingPageData) {
    notFound();
  }

  return <LandingPage data={landingPageData} />;
}
