import type { Metadata } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  metadataBase: new URL('https://helevon-landing.helevon.org'),
  title: "Helevon Landing Pages - Build Your MVP with Equity Partnership",
  description: "Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity. World-class development for ambitious startups.",
  keywords: "MVP development, startup equity, founder partnership, app development, tech startup, venture building",
  authors: [{ name: "Helevon Technologies" }],
  creator: "Helevon Technologies",
  publisher: "Helevon Technologies",
  robots: "index, follow",
  icons: {
    icon: [
      {
        url: "/favicon.png",
        sizes: "247x282",
        type: "image/png",
      },
      {
        url: "/favicon-192.png",
        sizes: "192x192",
        type: "image/png",
      },
    ],
    apple: [
      {
        url: "/apple-touch-icon.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
    shortcut: "/favicon.ico",
  },
  manifest: "/manifest.json",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://helevon-landing.helevon.org",
    siteName: "Helevon Landing Pages",
    title: "Build Your MVP with Equity Partnership | Helevon",
    description: "Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity.",
    images: [
      {
        url: "/og-default.jpg",
        width: 1200,
        height: 630,
        alt: "Helevon - MVP Development with Equity Partnership",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Build Your MVP with Equity Partnership | Helevon",
    description: "Partner with experienced founders to build your MVP. Pay <20% upfront, we take equity.",
    images: ["/og-default.jpg"],
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#3B82F6',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased bg-white text-gray-900 selection:bg-blue-100 selection:text-blue-900`}
      >
        <div className="min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
