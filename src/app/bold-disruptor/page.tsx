import { Metadata } from 'next';
import { LandingPage } from '@/components/LandingPage';
import { getLandingPageBySlug } from '@/data/landing-pages';
import { notFound } from 'next/navigation';

export const metadata: Metadata = {
  title: 'Turn Your Startup Dream Into Reality | Helevon - Bold Disruptor',
  description: 'Build world-class MVPs for <20% upfront cost. Keep 80% equity. We bet on your success because we believe in building the future together.',
  keywords: 'startup MVP, equity partnership, disruptive technology, app development, venture building',
  openGraph: {
    title: 'Turn Your Startup Dream Into Reality | Helevon',
    description: 'Build world-class MVPs for <20% upfront cost. Keep 80% equity. We bet on your success.',
    type: 'website',
    url: '/bold-disruptor',
    images: [
      {
        url: '/og-bold-disruptor.jpg',
        width: 1200,
        height: 630,
        alt: 'Helevon - Bold Disruptor MVP Development',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Turn Your Startup Dream Into Reality | Helevon',
    description: 'Build world-class MVPs for <20% upfront cost. Keep 80% equity.',
    images: ['/og-bold-disruptor.jpg'],
  },
};

export default function BoldDisruptorPage() {
  const landingPageData = getLandingPageBySlug('bold-disruptor');
  
  if (!landingPageData) {
    notFound();
  }

  return <LandingPage data={landingPageData} />;
}
