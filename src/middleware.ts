import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Redirect root path to helevon.com
  if (pathname === '/') {
    return NextResponse.redirect('https://helevon.com');
  }

  // Allow landing page routes to pass through
  const allowedPaths = [
    '/founder-focused',
    '/bold-disruptor',
    '/api',
    '/_next',
    '/favicon.ico',
    '/favicon.png',
    '/favicon-192.png',
    '/favicon-512.png',
    '/apple-touch-icon.png',
    '/manifest.json',
    '/robots.txt',
    '/sitemap.xml',
    '/videos/group-of-people.mp4',
    '/videos/startup-man.mp4'
  ];

  const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path));
  
  if (!isAllowedPath) {
    // Redirect unknown paths to helevon.com
    return NextResponse.redirect('https://helevon.com');
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon files and manifest
     */
    '/((?!_next/static|_next/image|favicon|manifest.json|apple-touch-icon).*)',
  ],
};
