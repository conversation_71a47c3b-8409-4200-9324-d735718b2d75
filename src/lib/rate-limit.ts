// Simple in-memory rate limiting (for production, use Redis or database)
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

export interface RateLimitResult {
  success: boolean;
  remaining: number;
  resetTime: number;
  minutesUntilReset?: number;
}

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 3,
  windowMinutes: number = 30
): RateLimitResult {
  const now = Date.now();
  const windowMs = windowMinutes * 60 * 1000;
  const resetTime = now + windowMs;

  const entry = rateLimitStore.get(identifier);

  // If no entry exists or the window has expired, create a new one
  if (!entry || now >= entry.resetTime) {
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime
    });
    
    return {
      success: true,
      remaining: maxRequests - 1,
      resetTime
    };
  }

  // If within the window and under the limit
  if (entry.count < maxRequests) {
    entry.count++;
    rateLimitStore.set(identifier, entry);
    
    return {
      success: true,
      remaining: maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }

  // Rate limit exceeded
  const minutesUntilReset = Math.ceil((entry.resetTime - now) / (60 * 1000));
  
  return {
    success: false,
    remaining: 0,
    resetTime: entry.resetTime,
    minutesUntilReset
  };
}

// Clean up expired entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now >= entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes
