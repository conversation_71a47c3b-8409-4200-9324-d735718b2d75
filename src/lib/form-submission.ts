import { FormData, FormSubmissionResponse } from '@/types';

// Custom email validation utility
interface EmailValidationResult {
  valid: boolean;
  reason?: string;
  suggestions?: string[];
}

// Common email domain typos and their corrections
const domainCorrections: Record<string, string> = {
  'gmai.com': 'gmail.com',
  'gmial.com': 'gmail.com',
  'gmaill.com': 'gmail.com',
  'gmil.com': 'gmail.com',
  'yahooo.com': 'yahoo.com',
  'yaho.com': 'yahoo.com',
  'hotmial.com': 'hotmail.com',
  'hotmil.com': 'hotmail.com',
  'outlok.com': 'outlook.com',
  'outloo.com': 'outlook.com',
  'iclod.com': 'icloud.com',
  'icoud.com': 'icloud.com',
};

// Common disposable email domains (subset)
const disposableDomains = new Set([
  '10minutemail.com',
  'tempmail.org',
  'guerrillamail.com',
  'mailinator.com',
  'throwaway.email',
  'temp-mail.org',
  'yopmail.com',
  'maildrop.cc',
  'sharklasers.com',
  'guerrillamailblock.com',
  'pokemail.net',
  'spam4.me',
  'bccto.me',
  'chacuo.net',
  'dispostable.com',
  'fakeinbox.com',
  'hide.biz.st',
  'mytrashmail.com',
  'nobulk.com',
  'sogetthis.com',
  'spambog.com',
  'spambog.de',
  'spambog.ru',
  'spamgourmet.com',
  'spamhole.com',
  'spamify.com',
  'spamthisplease.com',
  'superrito.com',
  'tempemail.com',
  'tempinbox.com',
  'thankyou2010.com',
  'trash-amil.com',
  'trashmail.net',
  'wegwerfmail.de',
  'wegwerfmail.net',
  'wegwerfmail.org',
  'wh4f.org',
  'whyspam.me',
  'willselfdestruct.com',
  'xoxy.net',
  'yogamaven.com',
  'zoemail.org',
]);

// Basic email regex validation
function validateEmailRegex(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
}

// Check for common typos and suggest corrections
function checkTypos(email: string): { hasTypo: boolean; suggestions: string[] } {
  const [localPart, domain] = email.split('@');
  if (!domain) return { hasTypo: false, suggestions: [] };

  const suggestions: string[] = [];

  // Check for exact domain matches in corrections
  if (domainCorrections[domain.toLowerCase()]) {
    suggestions.push(`${localPart}@${domainCorrections[domain.toLowerCase()]}`);
    return { hasTypo: true, suggestions };
  }

  // Check for similar domains (simple Levenshtein distance)
  const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com'];
  for (const commonDomain of commonDomains) {
    if (getLevenshteinDistance(domain.toLowerCase(), commonDomain) <= 2 && domain.toLowerCase() !== commonDomain) {
      suggestions.push(`${localPart}@${commonDomain}`);
    }
  }

  return { hasTypo: suggestions.length > 0, suggestions };
}

// Simple Levenshtein distance calculation
function getLevenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

// Check if email is from a disposable domain
function isDisposableEmail(email: string): boolean {
  const domain = email.split('@')[1]?.toLowerCase();
  return domain ? disposableDomains.has(domain) : false;
}

// Check MX records (simplified - in a real app you'd use a backend service)
async function checkMXRecords(domain: string): Promise<boolean> {
  try {
    // In a browser environment, we can't directly check DNS records
    // This would typically be done on the backend
    // For now, we'll use a simple domain validation
    const response = await fetch(`https://dns.google/resolve?name=${domain}&type=MX`);
    const data = await response.json();
    return data.Answer && data.Answer.length > 0;
  } catch {
    // If we can't check MX records, assume valid for common domains
    const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'icloud.com', 'protonmail.com'];
    return commonDomains.includes(domain.toLowerCase());
  }
}

// Main email validation function
export async function validateEmailDomain(email: string): Promise<boolean> {
  try {
    const result = await validateEmailAdvanced(email);
    return result.valid;
  } catch (error) {
    console.error('Email validation error:', error);
    // Fallback to basic regex validation
    return validateEmailRegex(email);
  }
}

// Advanced email validation with detailed results
export async function validateEmailAdvanced(email: string): Promise<EmailValidationResult> {
  // Step 1: Basic regex validation
  if (!validateEmailRegex(email)) {
    return {
      valid: false,
      reason: 'Invalid email format'
    };
  }

  // Step 2: Check for typos
  const typoCheck = checkTypos(email);
  if (typoCheck.hasTypo) {
    return {
      valid: false,
      reason: 'Possible typo detected',
      suggestions: typoCheck.suggestions
    };
  }

  // Step 3: Check for disposable email
  if (isDisposableEmail(email)) {
    return {
      valid: false,
      reason: 'Disposable email address not allowed'
    };
  }

  // Step 4: Check MX records
  const domain = email.split('@')[1];
  if (domain) {
    const hasMX = await checkMXRecords(domain);
    if (!hasMX) {
      return {
        valid: false,
        reason: 'Domain does not have valid MX records'
      };
    }
  }

  // Step 5: All checks passed
  return {
    valid: true
  };
}

// CRM Integration Templates
export class CRMIntegration {
  // HubSpot Integration
  static async submitToHubSpot(data: FormData): Promise<FormSubmissionResponse> {
    try {
      const hubspotData = {
        fields: [
          { name: 'firstname', value: data.name.split(' ')[0] },
          { name: 'lastname', value: data.name.split(' ').slice(1).join(' ') || '' },
          { name: 'email', value: data.email },
          { name: 'phone', value: data.phone || '' },
          { name: 'company', value: data.company || '' },
          { name: 'message', value: data.ideaSummary },
          { name: 'budget_range', value: data.estimatedBudget },
          { name: 'timeline', value: data.timeline },
          { name: 'additional_info', value: data.additionalInfo || '' },
          { name: 'lead_source', value: 'Landing Page' },
          { name: 'lifecyclestage', value: 'lead' }
        ],
        context: {
          pageUri: window.location.href,
          pageName: document.title
        }
      };

      const response = await fetch('/api/hubspot/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(hubspotData)
      });

      if (!response.ok) {
        throw new Error('Failed to submit to HubSpot');
      }

      return {
        success: true,
        message: 'Successfully submitted to HubSpot',
        data: await response.json()
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'HubSpot submission failed'
      };
    }
  }

  // Salesforce Integration
  static async submitToSalesforce(data: FormData): Promise<FormSubmissionResponse> {
    try {
      const salesforceData = {
        FirstName: data.name.split(' ')[0],
        LastName: data.name.split(' ').slice(1).join(' ') || 'Unknown',
        Email: data.email,
        Phone: data.phone || '',
        Company: data.company || 'Unknown',
        Description: data.ideaSummary,
        Budget__c: data.estimatedBudget,
        Timeline__c: data.timeline,
        Additional_Info__c: data.additionalInfo || '',
        LeadSource: 'Landing Page',
        Status: 'New'
      };

      const response = await fetch('/api/salesforce/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(salesforceData)
      });

      if (!response.ok) {
        throw new Error('Failed to submit to Salesforce');
      }

      return {
        success: true,
        message: 'Successfully submitted to Salesforce',
        data: await response.json()
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Salesforce submission failed'
      };
    }
  }

  // Pipedrive Integration
  static async submitToPipedrive(data: FormData): Promise<FormSubmissionResponse> {
    try {
      const pipedriveData = {
        title: `${data.name} - ${data.company || 'New Lead'}`,
        person_id: null, // Will be created if person doesn't exist
        org_id: null,
        value: data.estimatedBudget,
        currency: 'USD',
        stage_id: 1, // First stage of your pipeline
        status: 'open',
        expected_close_date: null,
        probability: null,
        lost_reason: null,
        visible_to: '3', // Visible to everyone
        add_time: new Date().toISOString(),
        notes: [
          {
            content: `Idea: ${data.ideaSummary}\n\nBudget: ${data.estimatedBudget}\nTimeline: ${data.timeline}\n\nAdditional Info: ${data.additionalInfo || 'None'}`,
            pinned_to_deal_flag: true
          }
        ],
        person: {
          name: data.name,
          email: [{ value: data.email, primary: true }],
          phone: data.phone ? [{ value: data.phone, primary: true }] : [],
          org_name: data.company || null
        }
      };

      const response = await fetch('/api/pipedrive/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pipedriveData)
      });

      if (!response.ok) {
        throw new Error('Failed to submit to Pipedrive');
      }

      return {
        success: true,
        message: 'Successfully submitted to Pipedrive',
        data: await response.json()
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Pipedrive submission failed'
      };
    }
  }

  // Airtable Integration
  static async submitToAirtable(data: FormData): Promise<FormSubmissionResponse> {
    try {
      const airtableData = {
        records: [
          {
            fields: {
              'Name': data.name,
              'Email': data.email,
              'Phone': data.phone || '',
              'Company': data.company || '',
              'Idea Summary': data.ideaSummary,
              'Budget Range': data.estimatedBudget,
              'Timeline': data.timeline,
              'Additional Info': data.additionalInfo || '',
              'Lead Source': 'Landing Page',
              'Status': 'New',
              'Submitted At': new Date().toISOString(),
              'Follow Up Required': true
            }
          }
        ]
      };

      const response = await fetch('/api/airtable/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(airtableData)
      });

      if (!response.ok) {
        throw new Error('Failed to submit to Airtable');
      }

      return {
        success: true,
        message: 'Successfully submitted to Airtable',
        data: await response.json()
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Airtable submission failed'
      };
    }
  }
}

// Email notification service
export async function sendEmailNotification(data: FormData): Promise<FormSubmissionResponse> {
  try {
    const emailData = {
      to: process.env.NOTIFICATION_EMAIL || '<EMAIL>',
      subject: `New Lead: ${data.name} - ${data.company || 'Startup Idea'}`,
      html: `
        <h2>New Lead Submission</h2>
        <p><strong>Name:</strong> ${data.name}</p>
        <p><strong>Email:</strong> ${data.email}</p>
        <p><strong>Phone:</strong> ${data.phone || 'Not provided'}</p>
        <p><strong>Company:</strong> ${data.company || 'Not provided'}</p>
        <p><strong>Budget Range:</strong> ${data.estimatedBudget}</p>
        <p><strong>Timeline:</strong> ${data.timeline}</p>
        
        <h3>Idea Summary:</h3>
        <p>${data.ideaSummary}</p>
        
        ${data.additionalInfo ? `
          <h3>Additional Information:</h3>
          <p>${data.additionalInfo}</p>
        ` : ''}
        
        <p><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Source:</strong> Landing Page</p>
      `,
      replyTo: data.email
    };

    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData)
    });

    if (!response.ok) {
      throw new Error('Failed to send email notification');
    }

    return {
      success: true,
      message: 'Email notification sent successfully'
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Email notification failed'
    };
  }
}

// Main form submission handler
export async function submitForm(data: FormData): Promise<FormSubmissionResponse> {
  try {
    // Validate email domain
    const isValidEmail = await validateEmailDomain(data.email);
    if (!isValidEmail) {
      throw new Error('Please enter a valid email address');
    }

    // Submit to our API endpoint
    const response = await fetch('/api/submit-form', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Form submission failed');
    }

    return result;
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Form submission failed'
    };
  }
}
