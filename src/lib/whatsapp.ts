export function generateWhatsAppURL(message?: string): string {
  const phoneNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '+447123456789';
  
  const defaultMessage = `Hi, I am reaching out in regards to the opportunity I saw regarding the equity partnership model for MVP development. I would like to discuss my startup idea and explore how we can work together to bring it to life.`;
  
  const finalMessage = message || defaultMessage;
  
  // Remove the + from phone number for WhatsApp URL
  const cleanPhoneNumber = phoneNumber.replace('+', '');
  
  // Encode the message for URL
  const encodedMessage = encodeURIComponent(finalMessage);
  
  return `https://wa.me/${cleanPhoneNumber}?text=${encodedMessage}`;
}

export function openWhatsApp(message?: string): void {
  const url = generateWhatsAppURL(message);
  window.open(url, '_blank');
}
