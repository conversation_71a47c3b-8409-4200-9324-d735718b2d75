export interface CRMLeadData {
  firstName: string;
  lastName: string;
  emailAddress: string;
  description: string;
  phoneNumber?: string;
}

export interface CRMResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
}

export function parseFullName(fullName: string): { firstName: string; lastName: string } {
  const nameParts = fullName.trim().split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';
  
  return { firstName, lastName };
}

export function formatContactFormData(data: {
  name: string;
  email: string;
  phone?: string;
  message?: string;
  company?: string;
  budget?: string;
  timeline?: string;
  ideaSummary?: string;
}): CRMLeadData {
  const { firstName, lastName } = parseFullName(data.name);

  let description = `Contact Form Submission:\n\n`;

  if (data.ideaSummary) {
    description += `Idea Summary: ${data.ideaSummary}\n`;
  }

  if (data.message) {
    description += `Message: ${data.message}\n`;
  }

  if (data.company) {
    description += `Company: ${data.company}\n`;
  }

  if (data.budget) {
    description += `Estimated Budget: ${data.budget}\n`;
  }

  if (data.timeline) {
    description += `Timeline: ${data.timeline}\n`;
  }

  description += `\nSource: Landing Page Contact Form`;

  return {
    firstName,
    lastName,
    emailAddress: data.email,
    description,
    phoneNumber: data.phone || "NA"
  };
}

export function formatScheduleCallData(data: {
  name: string;
  email: string;
  phone?: string;
  preferredDate: string;
  preferredTime: string;
  message?: string;
}): CRMLeadData {
  const { firstName, lastName } = parseFullName(data.name);

  let description = `Schedule Call Request:\n\n`;
  description += `Preferred Date: ${data.preferredDate}\n`;
  description += `Preferred Time: ${data.preferredTime} (UK Time)\n`;

  if (data.message) {
    description += `Message: ${data.message}\n`;
  }

  description += `\nSource: Landing Page Schedule Call`;

  return {
    firstName,
    lastName,
    emailAddress: data.email,
    description,
    phoneNumber: data.phone || "NA"
  };
}

export async function submitToCRM(leadData: CRMLeadData): Promise<CRMResponse> {
  try {
    const apiKey = process.env.HELEVON_CRM_API_KEY;
    const baseUrl = process.env.HELEVON_CRM_BASE_URL;
    
    if (!apiKey || !baseUrl) {
      throw new Error('CRM configuration missing');
    }
    
    const response = await fetch(`${baseUrl}/LeadCapture/${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(leadData),
    });
    
    if (!response.ok) {
      throw new Error(`CRM API error: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    
    return {
      success: true,
      message: 'Lead submitted successfully',
      data: result
    };
  } catch (error) {
    console.error('CRM submission error:', error);
    
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to submit to CRM'
    };
  }
}
