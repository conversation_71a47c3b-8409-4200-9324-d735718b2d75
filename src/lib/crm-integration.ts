export interface CRMLeadData {
  firstName: string;
  lastName: string;
  emailAddress: string;
  description: string;
  phoneNumber?: string;
}

export interface CRMResponse {
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
}

export function parseFullName(fullName: string): { firstName: string; lastName: string } {
  const nameParts = fullName.trim().split(' ');
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';
  
  return { firstName, lastName };
}

export function formatContactFormData(data: {
  name: string;
  email: string;
  phone?: string;
  message?: string;
  company?: string;
  budget?: string;
  timeline?: string;
  ideaSummary?: string;
}): CRMLeadData {
  const { firstName, lastName } = parseFullName(data.name);

  let description = `Contact Form Submission:\n\n`;

  if (data.ideaSummary) {
    description += `Idea Summary: ${data.ideaSummary}\n\n`;
  }

  if (data.message) {
    description += `Message: ${data.message}\n\n`;
  }

  if (data.company) {
    description += `Company: ${data.company}\n`;
  }

  if (data.budget) {
    description += `Estimated Budget: ${data.budget}\n`;
  }

  if (data.timeline) {
    description += `Timeline: ${data.timeline}\n`;
  }

  description += `\nSource: Landing Page Contact Form`;
  description += `\nSubmission Time: ${new Date().toISOString()}`;

  // Handle phone number - use empty string if not provided or invalid
  let phoneNumber = "";
  if (data.phone && data.phone.trim() && data.phone !== "NA") {
    phoneNumber = data.phone.trim();
  }

  return {
    firstName,
    lastName,
    emailAddress: data.email,
    description,
    phoneNumber
  };
}

export function formatScheduleCallData(data: {
  name: string;
  email: string;
  phone?: string;
  preferredDate: string;
  preferredTime: string;
  message?: string;
}): CRMLeadData {
  const { firstName, lastName } = parseFullName(data.name);

  let description = `Schedule Call Request:\n\n`;
  description += `Preferred Date: ${data.preferredDate}\n`;
  description += `Preferred Time: ${data.preferredTime} (UK Time)\n\n`;

  if (data.message) {
    description += `Additional Message: ${data.message}\n\n`;
  }

  description += `Source: Landing Page Schedule Call`;
  description += `\nSubmission Time: ${new Date().toISOString()}`;

  // Handle phone number - use empty string if not provided or invalid
  let phoneNumber = "";
  if (data.phone && data.phone.trim() && data.phone !== "NA") {
    phoneNumber = data.phone.trim();
  }

  return {
    firstName,
    lastName,
    emailAddress: data.email,
    description,
    phoneNumber
  };
}

export async function submitToCRM(leadData: CRMLeadData): Promise<CRMResponse> {
  try {
    const apiKey = process.env.HELEVON_CRM_API_KEY;
    const baseUrl = process.env.HELEVON_CRM_BASE_URL;

    console.log('=== CRM SUBMISSION DEBUG ===');
    console.log('API Key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'MISSING');
    console.log('Base URL:', baseUrl);

    if (!apiKey || !baseUrl) {
      throw new Error('CRM configuration missing');
    }

    const url = `${baseUrl}/LeadCapture/${apiKey}`;
    console.log('Full URL:', url);
    console.log('Payload:', JSON.stringify(leadData, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(leadData),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (!response.ok) {
      throw new Error(`CRM API error: ${response.status} ${response.statusText} - ${responseText}`);
    }

    let result;
    try {
      result = JSON.parse(responseText);
    } catch {
      result = responseText; // If response is not JSON, use as string
    }

    return {
      success: true,
      message: 'Lead submitted successfully',
      data: result
    };
  } catch (error) {
    console.error('CRM submission error:', error);

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to submit to CRM'
    };
  }
}
