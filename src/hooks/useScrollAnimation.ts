'use client';

import { useEffect, useRef } from 'react';
import { useInView, useAnimation } from 'framer-motion';

export function useScrollAnimation(threshold: number = 0.1, triggerOnce: boolean = true) {
  const ref = useRef(null);
  const isInView = useInView(ref, { amount: threshold, once: triggerOnce });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    } else {
      controls.start('hidden');
    }
  }, [isInView, controls]);

  return { ref, controls, isInView };
}

export function useParallax(speed: number = 0.5) {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const parallax = scrolled * speed;
      element.style.transform = `translateY(${parallax}px)`;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return ref;
}



export function useTypewriter(text: string, speed: number = 50) {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      const chars = text.split('');
      controls.start({
        opacity: chars.map((_, i) => (i < chars.length ? 1 : 0)),
        transition: {
          duration: (text.length * speed) / 1000,
          ease: 'linear'
        }
      });
    }
  }, [isInView, text, speed, controls]);

  return { ref, controls };
}
