'use client';

import { useState, useEffect } from 'react';

export function useIdeaModal() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    // Check if modal has been shown before in this session
    const modalShown = sessionStorage.getItem('ideaModalShown');

    if (modalShown) {
      setHasShown(true);
      return;
    }

    let hasScrolledFromHero = false;

    // Show modal after 10 seconds
    const timeoutId = setTimeout(() => {
      if (!hasShown) {
        setIsModalOpen(true);
        setHasShown(true);
        sessionStorage.setItem('ideaModalShown', 'true');
      }
    }, 10000); // 10 seconds

    // Show modal when user scrolls past hero section
    const handleScroll = () => {
      const heroSection = document.querySelector('[data-section="hero"]');
      if (heroSection && !hasScrolledFromHero && !hasShown) {
        const heroBottom = heroSection.getBoundingClientRect().bottom;

        if (heroBottom < window.innerHeight * 0.5) { // When 50% of hero is scrolled past
          hasScrolledFromHero = true;
          clearTimeout(timeoutId);
          setIsModalOpen(true);
          setHasShown(true);
          sessionStorage.setItem('ideaModalShown', 'true');
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [hasShown]);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const openModal = () => {
    setIsModalOpen(true);
    setHasShown(true);
    sessionStorage.setItem('ideaModalShown', 'true');
  };

  return {
    isModalOpen,
    closeModal,
    openModal,
    hasShown
  };
}
