'use client';

import { useState, useEffect } from 'react';

export function useIdeaModal() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    // Check if modal has been shown before in this session
    const modalShown = sessionStorage.getItem('ideaModalShown');
    
    if (modalShown) {
      setHasShown(true);
      return;
    }

    // Show modal after user has been on the page for 30 seconds
    const timer = setTimeout(() => {
      if (!hasShown) {
        setIsModalOpen(true);
        setHasShown(true);
        sessionStorage.setItem('ideaModalShown', 'true');
      }
    }, 30000); // 30 seconds

    return () => clearTimeout(timer);
  }, [hasShown]);

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const openModal = () => {
    setIsModalOpen(true);
    setHasShown(true);
    sessionStorage.setItem('ideaModalShown', 'true');
  };

  return {
    isModalOpen,
    closeModal,
    openModal,
    hasShown
  };
}
