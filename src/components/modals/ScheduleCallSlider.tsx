'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Calendar, Clock, User, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const scheduleSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  preferredDate: z.string().min(1, 'Please select a date'),
  preferredTime: z.string().min(1, 'Please select a time'),
  timezone: z.string().optional().default('Europe/London'),
  message: z.string().optional(),
});

type ScheduleFormData = z.infer<typeof scheduleSchema>;

interface ScheduleCallSliderProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ScheduleCallSlider({ isOpen, onClose }: ScheduleCallSliderProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    resolver: zodResolver(scheduleSchema)
  });

  // Generate available time slots
  const generateTimeSlots = () => {
    // Monday to Friday: 8am - 7pm UK time
    const weekdaySlots = [];
    for (let hour = 8; hour <= 19; hour++) {
      weekdaySlots.push(`${hour.toString().padStart(2, '0')}:00`);
      if (hour < 19) {
        weekdaySlots.push(`${hour.toString().padStart(2, '0')}:30`);
      }
    }
    
    // Saturday: 10am - 4pm UK time
    const saturdaySlots = [];
    for (let hour = 10; hour <= 16; hour++) {
      saturdaySlots.push(`${hour.toString().padStart(2, '0')}:00`);
      if (hour < 16) {
        saturdaySlots.push(`${hour.toString().padStart(2, '0')}:30`);
      }
    }

    return { weekdaySlots, saturdaySlots };
  };

  const { weekdaySlots, saturdaySlots } = generateTimeSlots();
  const selectedDate = watch('preferredDate');

  const getAvailableSlots = () => {
    if (!selectedDate) return [];
    
    const date = new Date(selectedDate);
    const dayOfWeek = date.getDay();
    
    // Sunday (0) - not available
    if (dayOfWeek === 0) return [];
    
    // Saturday (6) - limited hours
    if (dayOfWeek === 6) return saturdaySlots;
    
    // Monday to Friday (1-5) - full hours
    return weekdaySlots;
  };

  const onSubmit = async (data: ScheduleFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Submit to our API
      const response = await fetch('/api/submit-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          type: 'schedule'
        }),
      });

      const result = await response.json();

      if (response.status === 429) {
        setSubmitStatus('error');
        return;
      }

      if (!response.ok) {
        throw new Error(result.message || 'Submission failed');
      }

      setSubmitStatus('success');
      reset();

      // Close slider after success
      setTimeout(() => {
        onClose();
        setSubmitStatus('idle');
      }, 2000);
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get minimum date (today)
  const today = new Date().toISOString().split('T')[0];
  
  // Get maximum date (3 months from now)
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);
  const maxDateString = maxDate.toISOString().split('T')[0];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          />

          {/* Slider */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-2xl z-50 overflow-y-auto"
          >
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 p-6 z-10">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Schedule a Call</h2>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>
              <p className="text-gray-600 mt-2">
                Book a 30-minute discovery call to discuss your project
              </p>
            </div>

            {/* Content */}
            <div className="p-6">
              {submitStatus === 'success' ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-8"
                >
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Calendar className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Call Scheduled!
                  </h3>
                  <p className="text-gray-600">
                    We&apos;ll send you a calendar invite and call details shortly.
                  </p>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Personal Info */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                      <User className="w-5 h-5 mr-2 text-blue-600" />
                      Your Information
                    </h3>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        {...register('name')}
                        type="text"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                        placeholder="John Doe"
                      />
                      {errors.name && (
                        <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        {...register('email')}
                        type="email"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                        placeholder="<EMAIL>"
                      />
                      {errors.email && (
                        <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number (Optional)
                      </label>
                      <input
                        {...register('phone')}
                        type="tel"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                        placeholder="+44 7123 456789"
                      />
                    </div>
                  </div>

                  {/* Scheduling */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                      <Calendar className="w-5 h-5 mr-2 text-blue-600" />
                      Preferred Date & Time
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date *
                      </label>
                      <input
                        {...register('preferredDate')}
                        type="date"
                        min={today}
                        max={maxDateString}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                      />
                      {errors.preferredDate && (
                        <p className="mt-1 text-sm text-red-600">{errors.preferredDate.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Time (UK Time) *
                      </label>
                      <select
                        {...register('preferredTime')}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                      >
                        <option value="">Select a time</option>
                        {getAvailableSlots().map(slot => (
                          <option key={slot} value={slot}>
                            {slot}
                          </option>
                        ))}
                      </select>
                      {errors.preferredTime && (
                        <p className="mt-1 text-sm text-red-600">{errors.preferredTime.message}</p>
                      )}
                      {selectedDate && getAvailableSlots().length === 0 && (
                        <p className="mt-1 text-sm text-amber-600">
                          No available slots for this date. Please choose Monday-Friday (8am-7pm) or Saturday (10am-4pm).
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <MessageSquare className="w-4 h-4 inline mr-1" />
                      What would you like to discuss? (Optional)
                    </label>
                    <textarea
                      {...register('message')}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none"
                      placeholder="Brief description of your project or questions..."
                    />
                  </div>

                  {/* Submit */}
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    variant="gradient"
                    size="lg"
                    className="w-full"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Scheduling...
                      </>
                    ) : (
                      <>
                        <Clock className="w-4 h-4 mr-2" />
                        Schedule Call
                      </>
                    )}
                  </Button>

                  {submitStatus === 'error' && (
                    <p className="text-sm text-red-600 text-center">
                      Something went wrong. Please try again.
                    </p>
                  )}
                </form>
              )}

              {/* Availability Info */}
              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Our Availability</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>📅 Monday - Friday: 8:00 AM - 7:00 PM (UK Time)</p>
                  <p>📅 Saturday: 10:00 AM - 4:00 PM (UK Time)</p>
                  <p>📅 Sunday: Closed</p>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
