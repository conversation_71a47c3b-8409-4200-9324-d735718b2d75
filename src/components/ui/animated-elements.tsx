'use client';

import { motion, HTMLMotionProps } from 'framer-motion';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { fadeInUp, fadeInLeft, fadeInRight, scaleIn } from '@/lib/animations';
import { ReactNode } from 'react';

interface AnimatedElementProps extends HTMLMotionProps<'div'> {
  children: ReactNode;
  animation?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn';
  delay?: number;
  threshold?: number;
}

export function AnimatedElement({ 
  children, 
  animation = 'fadeInUp', 
  delay = 0,
  threshold = 0.1,
  ...props 
}: AnimatedElementProps) {
  const { ref, controls } = useScrollAnimation(threshold);

  const animations = {
    fadeInUp,
    fadeInLeft,
    fadeInRight,
    scaleIn
  };

  const selectedAnimation = animations[animation];

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={selectedAnimation}
      transition={{ delay }}
      {...props}
    >
      {children}
    </motion.div>
  );
}

interface FloatingElementProps {
  children: ReactNode;
  duration?: number;
  yOffset?: number;
  xOffset?: number;
  className?: string;
}

export function FloatingElement({ 
  children, 
  duration = 3, 
  yOffset = 10, 
  xOffset = 0,
  className = ''
}: FloatingElementProps) {
  return (
    <motion.div
      animate={{
        y: [0, -yOffset, 0],
        x: [0, xOffset, 0],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut'
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface PulsingElementProps {
  children: ReactNode;
  scale?: number;
  duration?: number;
  className?: string;
}

export function PulsingElement({ 
  children, 
  scale = 1.05, 
  duration = 2,
  className = ''
}: PulsingElementProps) {
  return (
    <motion.div
      animate={{
        scale: [1, scale, 1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut'
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface RotatingElementProps {
  children: ReactNode;
  duration?: number;
  degrees?: number;
  className?: string;
}

export function RotatingElement({ 
  children, 
  duration = 10, 
  degrees = 360,
  className = ''
}: RotatingElementProps) {
  return (
    <motion.div
      animate={{
        rotate: [0, degrees],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'linear'
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface CountUpProps {
  end: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
  className?: string;
}

export function CountUp({
  end,
  suffix = '',
  prefix = '',
  className = ''
}: Omit<CountUpProps, 'duration'>) {
  const { ref, isInView } = useScrollAnimation(0.3);

  return (
    <motion.span
      ref={ref}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      {prefix}
      {end.toLocaleString()}
      {suffix}
    </motion.span>
  );
}

interface TypewriterProps {
  text: string;
  speed?: number;
  className?: string;
  cursor?: boolean;
}

export function Typewriter({ 
  text, 
  speed = 50, 
  className = '',
  cursor = true
}: TypewriterProps) {
  const { ref, controls } = useScrollAnimation(0.3);

  return (
    <motion.span
      ref={ref}
      initial="hidden"
      animate={controls}
      className={className}
    >
      <motion.span
        variants={{
          hidden: { width: 0 },
          visible: { width: 'auto' }
        }}
        transition={{ 
          duration: (text.length * speed) / 1000,
          ease: 'linear'
        }}
        style={{ overflow: 'hidden', display: 'inline-block' }}
      >
        {text}
      </motion.span>
      {cursor && (
        <motion.span
          animate={{ opacity: [1, 0, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="ml-1"
        >
          |
        </motion.span>
      )}
    </motion.span>
  );
}

interface MagneticElementProps {
  children: ReactNode;
  strength?: number;
  className?: string;
}

export function MagneticElement({ 
  children, 
  strength = 0.3,
  className = ''
}: MagneticElementProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        e.currentTarget.style.transform = `translate(${x * strength}px, ${y * strength}px)`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translate(0px, 0px)';
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface ParallaxElementProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function ParallaxElement({ 
  children, 
  speed = 0.5,
  className = ''
}: ParallaxElementProps) {
  return (
    <motion.div
      style={{
        y: useScrollAnimation().isInView ? 0 : -50 * speed
      }}
      transition={{ type: 'spring', stiffness: 100, damping: 30 }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface GlowEffectProps {
  children: ReactNode;
  color?: string;
  intensity?: number;
  className?: string;
}

export function GlowEffect({ 
  children, 
  color = '#3B82F6', 
  intensity = 20,
  className = ''
}: GlowEffectProps) {
  return (
    <motion.div
      whileHover={{
        boxShadow: `0 0 ${intensity}px ${color}`,
        transition: { duration: 0.3 }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}
