'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { fadeInUp } from '@/lib/animations';
import { FAQSection } from '@/types';
import { ChevronDown, Plus, Minus } from 'lucide-react';
import { ScheduleCallSlider } from '@/components/modals/ScheduleCallSlider';

interface FAQProps {
  data: FAQSection;
  variant?: 'default' | 'accordion' | 'cards';
}

export function FAQ({ data, variant = 'default' }: FAQProps) {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const [isScheduleSliderOpen, setIsScheduleSliderOpen] = useState(false);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const FAQItem = ({ faq, index }: { faq: { question: string; answer: string }; index: number }) => {
    const isOpen = openItems.includes(index);

    if (variant === 'cards') {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden"
        >
          <button
            onClick={() => toggleItem(index)}
            className="w-full p-6 text-left hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between group"
          >
            <h3 className="text-lg font-semibold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
              {faq.question}
            </h3>
            <motion.div
              animate={{ rotate: isOpen ? 45 : 0 }}
              transition={{ duration: 0.2 }}
              className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600"
            >
              <Plus className="w-4 h-4" />
            </motion.div>
          </button>

          <AnimatePresence>
            {isOpen && (
              <motion.div
                key={`faq-content-${index}`}
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="overflow-hidden"
              >
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2, delay: 0.1 }}
                  className="px-6 pb-6 leading-relaxed border-t border-gray-100 pt-4 relative z-10 faq-content"
                  style={{
                    color: '#4B5563',
                    backgroundColor: 'transparent',
                    textRendering: 'optimizeLegibility'
                  }}
                >
                  {faq.answer}
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      );
    }

    // Default and accordion variants
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
        className={`border-b border-gray-200 last:border-b-0 ${
          variant === 'accordion' ? 'bg-white rounded-lg mb-4 border border-gray-200 shadow-sm overflow-hidden' : ''
        }`}
      >
        <button
          onClick={() => toggleItem(index)}
          className={`w-full text-left hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between group ${
            variant === 'accordion' ? 'p-6' : 'py-6'
          }`}
        >
          <h3 className="text-lg font-semibold text-gray-900 pr-4 group-hover:text-blue-600 transition-colors">
            {faq.question}
          </h3>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="flex-shrink-0 text-gray-400 group-hover:text-blue-600 transition-colors"
          >
            {variant === 'accordion' ? (
              isOpen ? <Minus className="w-5 h-5" /> : <Plus className="w-5 h-5" />
            ) : (
              <ChevronDown className="w-5 h-5" />
            )}
          </motion.div>
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              key={`faq-content-${index}`}
              initial={{ height: 0 }}
              animate={{ height: 'auto' }}
              exit={{ height: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="overflow-hidden"
            >
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2, delay: 0.1 }}
                className={`leading-relaxed relative z-10 faq-content ${
                  variant === 'accordion' ? 'px-6 pb-6' : 'pb-6'
                }`}
                style={{
                  color: '#4B5563',
                  backgroundColor: '#ffffff',
                  textRendering: 'optimizeLegibility'
                }}
              >
                {faq.answer}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <section className={`py-20 ${
      variant === 'cards' ? 'bg-gradient-to-br from-gray-50 to-blue-50' : 'bg-white'
    }`}>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div variants={fadeInUp} className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {data.title}
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to know about our equity partnership model
            </p>
          </motion.div>

          <div className={variant === 'cards' ? 'space-y-6' : 'space-y-0'}>
            {data.faqs.map((faq, index) => (
              <FAQItem key={index} faq={faq} index={index} />
            ))}
          </div>

          {/* Additional help section */}
          <motion.div
            variants={fadeInUp}
            className="mt-16 text-center p-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-100"
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our team is here to help you understand how our equity partnership can work for your startup.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setIsScheduleSliderOpen(true)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Schedule a Call
              </button>
              <button className="px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium">
                Send us an Email
              </button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Schedule Call Slider */}
      <ScheduleCallSlider
        isOpen={isScheduleSliderOpen}
        onClose={() => setIsScheduleSliderOpen(false)}
      />
    </section>
  );
}
