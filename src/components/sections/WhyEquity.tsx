'use client';

import { motion } from 'framer-motion';
import { fadeInUp, fadeInLeft, fadeInRight, staggerContainer, staggerItem } from '@/lib/animations';
import { WhyEquitySection } from '@/types';
import { CheckCircle, Users, Zap, Target, Award, TrendingUp } from 'lucide-react';

interface WhyEquityProps {
  data: WhyEquitySection;
  variant?: 'default' | 'split' | 'centered';
}

export function WhyEquity({ data, variant = 'default' }: WhyEquityProps) {
  const icons = [CheckCircle, Users, Zap, Target, Award, TrendingUp];

  if (variant === 'split') {
    return (
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              {/* Left side - Content */}
              <motion.div
                variants={fadeInLeft}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.3 }}
              >
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                  {data.title}
                </h2>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  {data.subtitle}
                </p>

                <div className="space-y-6">
                  {data.points.map((point, index) => {
                    const IconComponent = icons[index % icons.length];
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                        viewport={{ once: true }}
                        className="flex items-start space-x-4"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                          <IconComponent className="w-4 h-4 text-white" />
                        </div>
                        <p className="text-gray-700 leading-relaxed">{point}</p>
                      </motion.div>
                    );
                  })}
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6, duration: 0.5 }}
                  viewport={{ once: true }}
                  className="mt-8 p-6 bg-white rounded-xl shadow-lg border border-blue-100"
                >
                  <p className="text-gray-800 font-medium italic">
                    &ldquo;{data.credibilityStatement}&rdquo;
                  </p>
                </motion.div>
              </motion.div>

              {/* Right side - Visual */}
              <motion.div
                variants={fadeInRight}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true, amount: 0.3 }}
                className="relative"
              >
                <div className="relative bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                  {/* Stats visualization */}
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">Partnership Impact</h3>
                      <p className="text-gray-600">Real results from our equity model</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center p-4 bg-blue-50 rounded-xl">
                        <div className="text-3xl font-bold text-blue-600 mb-1">80%</div>
                        <div className="text-sm text-gray-600">You Keep</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-xl">
                        <div className="text-3xl font-bold text-purple-600 mb-1">20%</div>
                        <div className="text-sm text-gray-600">We Take</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-xl">
                        <div className="text-3xl font-bold text-green-600 mb-1">3x</div>
                        <div className="text-sm text-gray-600">Faster Launch</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-xl">
                        <div className="text-3xl font-bold text-orange-600 mb-1">90%</div>
                        <div className="text-sm text-gray-600">Cost Savings</div>
                      </div>
                    </div>
                  </div>

                  {/* Floating elements */}
                  <motion.div
                    animate={{ y: [0, -10, 0] }}
                    transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
                    className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white shadow-lg"
                  >
                    <TrendingUp className="w-8 h-8" />
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (variant === 'centered') {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="max-w-4xl mx-auto text-center"
          >
            <motion.div variants={fadeInUp}>
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                {data.title}
              </h2>
              <p className="text-xl text-gray-600 mb-12 leading-relaxed">
                {data.subtitle}
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              {data.points.map((point, index) => {
                const IconComponent = icons[index % icons.length];
                return (
                  <motion.div
                    key={index}
                    variants={staggerItem}
                    className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-100"
                  >
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white mb-4 mx-auto">
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <p className="text-gray-700 leading-relaxed">{point}</p>
                  </motion.div>
                );
              })}
            </div>

            <motion.div
              variants={fadeInUp}
              className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white"
            >
              <p className="text-xl font-medium italic">
                &ldquo;{data.credibilityStatement}&rdquo;
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>
    );
  }

  // Default variant
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="max-w-6xl mx-auto"
        >
          <motion.div variants={fadeInUp} className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              {data.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {data.subtitle}
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {data.points.map((point, index) => {
              const IconComponent = icons[index % icons.length];
              return (
                <motion.div
                  key={index}
                  variants={staggerItem}
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                  className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white mb-4">
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <p className="text-gray-700 leading-relaxed">{point}</p>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            variants={fadeInUp}
            className="text-center bg-white p-8 rounded-2xl shadow-lg border border-gray-100"
          >
            <div className="max-w-3xl mx-auto">
              <p className="text-xl text-gray-800 font-medium italic mb-4">
                &ldquo;{data.credibilityStatement}&rdquo;
              </p>
              <div className="flex items-center justify-center space-x-2 text-gray-500">
                <Award className="w-5 h-5" />
                <span className="text-sm">Trusted by 100+ startups</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
