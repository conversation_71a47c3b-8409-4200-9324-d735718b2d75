'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';
import { ProjectsSection, MockProject } from '@/types';
import { ExternalLink, Users, DollarSign, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProjectModal } from '@/components/modals/ProjectModal';

interface ProjectsProps {
  data: ProjectsSection;
  variant?: 'default' | 'grid' | 'carousel';
}

export function Projects({ data, variant = 'default' }: ProjectsProps) {
  const [selectedProject, setSelectedProject] = useState<MockProject | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleProjectClick = (project: MockProject) => {
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  const ProjectCard = ({ project }: { project: MockProject }) => (
    <motion.div
      variants={staggerItem}
      whileHover="hover"
      onClick={() => handleProjectClick(project)}
      className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 group cursor-pointer"
    >
      {/* Project Image */}
      <div className="relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 overflow-hidden">
        {project.imageUrl ? (
          <Image
            src={project.imageUrl}
            alt={project.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-6xl font-bold text-blue-200">
              {project.name.charAt(0)}
            </div>
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
        
        {/* Category badge */}
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
          {project.category}
        </div>

        {/* External link icon */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileHover={{ opacity: 1, scale: 1 }}
          className="absolute top-4 right-4 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-gray-700"
        >
          <ExternalLink className="w-4 h-4" />
        </motion.div>
      </div>

      {/* Project Content */}
      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          {project.name}
        </h3>
        
        <p className="text-gray-600 mb-4 line-clamp-2">
          {project.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {project.tags.slice(0, 3).map((tag: string, tagIndex: number) => (
            <span
              key={tagIndex}
              className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full font-medium"
            >
              {tag}
            </span>
          ))}
          {project.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full font-medium">
              +{project.tags.length - 3}
            </span>
          )}
        </div>

        {/* Stats */}
        {project.stats && (
          <div className="flex justify-between items-center pt-4 border-t border-gray-100">
            {project.stats.users && (
              <div className="flex items-center text-sm text-gray-500">
                <Users className="w-4 h-4 mr-1" />
                {project.stats.users}
              </div>
            )}
            {project.stats.revenue && (
              <div className="flex items-center text-sm text-gray-500">
                <DollarSign className="w-4 h-4 mr-1" />
                {project.stats.revenue}
              </div>
            )}
            {project.stats.growth && (
              <div className="flex items-center text-sm text-green-600">
                <TrendingUp className="w-4 h-4 mr-1" />
                {project.stats.growth}
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );

  if (variant === 'grid') {
    return (
      <>
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
          <div className="container mx-auto px-4">
            <motion.div
              variants={staggerContainer}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.3 }}
              className="max-w-7xl mx-auto"
            >
              <motion.div variants={fadeInUp} className="text-center mb-16">
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                  {data.title}
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  {data.subtitle}
                </p>
              </motion.div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {data.projects.map((project) => (
                  <ProjectCard key={project.id} project={project} />
                ))}
              </div>

              <motion.div
                variants={fadeInUp}
                className="text-center mt-12"
              >
                <Button variant="outline" size="lg" className="group">
                  View All Projects
                  <ExternalLink className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Project Modal */}
        <ProjectModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          project={selectedProject}
        />
      </>
    );
  }

  // Default variant
  return (
    <>
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="max-w-6xl mx-auto"
          >
            <motion.div variants={fadeInUp} className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                {data.title}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {data.subtitle}
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 mb-12">
              {data.projects.slice(0, 4).map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>

            {/* Featured project */}
            {data.projects.length > 4 && (
              <motion.div
                variants={fadeInUp}
                className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative z-10">
                  <div className="flex flex-col lg:flex-row items-center gap-8">
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold mb-4">
                        Featured Success Story
                      </h3>
                      <p className="text-blue-100 mb-6">
                        See how we helped {data.projects[4]?.name || 'a startup'} achieve incredible growth with our equity partnership model.
                      </p>
                      <Button
                        variant="secondary"
                        size="lg"
                        onClick={() => data.projects[4] && handleProjectClick(data.projects[4])}
                      >
                        Read Case Study
                      </Button>
                    </div>
                    <div className="w-full lg:w-80 h-48 bg-white/10 rounded-xl flex items-center justify-center">
                      <div className="text-4xl">📊</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Project Modal */}
      <ProjectModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        project={selectedProject}
      />
    </>
  );
}
