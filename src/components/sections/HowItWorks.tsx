'use client';

import { motion } from 'framer-motion';
import { fadeInUp, staggerContainer, staggerItem, scaleInSpring } from '@/lib/animations';
import { HowItWorksSection } from '@/types';
import { Lightbulb, Wrench, TrendingUp, ArrowRight } from 'lucide-react';

interface HowItWorksProps {
  data: HowItWorksSection;
  variant?: 'default' | 'cards' | 'timeline';
}

const iconMap = {
  '💡': Lightbulb,
  '🛠️': Wrench,
  '📈': TrendingUp,
};

export function HowItWorks({ data, variant = 'default' }: HowItWorksProps) {
  const getIcon = (iconString: string, iconType: string) => {
    if (iconType === 'lucide' && iconMap[iconString as keyof typeof iconMap]) {
      const IconComponent = iconMap[iconString as keyof typeof iconMap];
      return <IconComponent className="w-8 h-8" />;
    }
    return <span className="text-4xl">{iconString}</span>;
  };

  if (variant === 'timeline') {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="max-w-4xl mx-auto"
          >
            <motion.div variants={fadeInUp} className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                {data.title}
              </h2>
            </motion.div>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-purple-500"></div>

              {data.steps.map((step, index) => (
                <motion.div
                  key={index}
                  variants={staggerItem}
                  className="relative flex items-start mb-12 last:mb-0"
                >
                  {/* Timeline dot */}
                  <div className="relative z-10 flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full text-white shadow-lg">
                    {getIcon(step.icon, step.iconType)}
                  </div>

                  {/* Content */}
                  <div className="ml-8 flex-1">
                    <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {step.description}
                      </p>
                    </div>
                  </div>

                  {/* Arrow for non-last items */}
                  {index < data.steps.length - 1 && (
                    <motion.div
                      animate={{ y: [0, 5, 0] }}
                      transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                      className="absolute left-6 -bottom-6 text-blue-500"
                    >
                      <ArrowRight className="w-6 h-6 rotate-90" />
                    </motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  if (variant === 'cards') {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <motion.div
            variants={staggerContainer}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="max-w-6xl mx-auto"
          >
            <motion.div variants={fadeInUp} className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                {data.title}
              </h2>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8">
              {data.steps.map((step, index) => (
                <motion.div
                  key={index}
                  variants={scaleInSpring}
                  whileHover={{ y: -10, transition: { duration: 0.2 } }}
                  className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 relative overflow-hidden group"
                >
                  {/* Background decoration */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-500"></div>
                  
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300">
                      {getIcon(step.icon, step.iconType)}
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {step.title}
                    </h3>
                    
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Step number */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm">
                    {index + 1}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    );
  }

  // Default variant
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="max-w-6xl mx-auto"
        >
          <motion.div variants={fadeInUp} className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              {data.title}
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-12">
            {data.steps.map((step, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
                className="text-center group"
              >
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white mb-6 mx-auto shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                >
                  {getIcon(step.icon, step.iconType)}
                </motion.div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {step.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>

                {/* Connecting arrow */}
                {index < data.steps.length - 1 && (
                  <motion.div
                    animate={{ x: [0, 10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, delay: index * 0.5 }}
                    className="hidden md:block absolute top-1/2 -right-6 text-blue-300"
                  >
                    <ArrowRight className="w-6 h-6" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
