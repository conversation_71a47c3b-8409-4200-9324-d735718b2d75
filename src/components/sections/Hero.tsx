'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { fadeInUp, fadeInDown, staggerContainer, staggerItem } from '@/lib/animations';
import { HeroSection } from '@/types';
import { scrollToElement } from '@/lib/utils';
import { ArrowRight, Play, Sparkles } from 'lucide-react';

interface HeroProps {
  data: HeroSection;
  variant?: 'default' | 'minimal' | 'bold';
}

export function Hero({ data, variant = 'default' }: HeroProps) {
  const handleCTAClick = () => {
    scrollToElement('contact-form', 80);
  };

  const getBackgroundClasses = () => {
    switch (data.backgroundType) {
      case 'gradient':
        return data.backgroundConfig.gradient || 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50';
      case 'image':
        return 'bg-cover bg-center bg-no-repeat';
      case 'video':
        // If video URL is not provided, fallback to gradient
        return data.backgroundConfig.videoUrl
          ? 'bg-black'
          : data.backgroundConfig.gradient || 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50';
      default:
        return 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50';
    }
  };

  const shouldShowVideo = () => {
    return data.backgroundType === 'video' && data.backgroundConfig.videoUrl;
  };

  const shouldShowOverlay = () => {
    return data.backgroundType === 'image' || data.backgroundType === 'video';
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'py-20 lg:py-32';
      case 'bold':
        return 'py-24 lg:py-40';
      default:
        return 'py-20 lg:py-36';
    }
  };

  return (
    <section
      data-section="hero"
      className={`relative min-h-screen flex items-center ${getBackgroundClasses()} ${getVariantClasses()}`}
      style={data.backgroundType === 'image' ? { backgroundImage: `url(${data.backgroundConfig.imageUrl})` } : {}}
    >
      {/* Background video */}
      {shouldShowVideo() && (
        <video
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src={data.backgroundConfig.videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      )}

      {/* Primary overlay for image/video */}
      {shouldShowOverlay() && (
        <div className="absolute inset-0 bg-black/50" />
      )}

      {/* Gradient overlay for better text readability */}
      {shouldShowOverlay() && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-black/40" />
      )}

      {/* Center focus overlay */}
      {shouldShowOverlay() && (
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />
      )}

      {/* Floating elements for visual interest */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, 0]
          }}
          transition={{ 
            duration: 6, 
            repeat: Infinity, 
            ease: 'easeInOut' 
          }}
          className="absolute top-20 left-10 text-blue-200 opacity-30"
        >
          <Sparkles size={24} />
        </motion.div>
        <motion.div
          animate={{ 
            y: [0, 15, 0],
            rotate: [0, -3, 0]
          }}
          transition={{ 
            duration: 8, 
            repeat: Infinity, 
            ease: 'easeInOut',
            delay: 2
          }}
          className="absolute top-40 right-20 text-purple-200 opacity-30"
        >
          <Sparkles size={32} />
        </motion.div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto text-center"
        >
          {/* Headline */}
          <motion.h1
            variants={fadeInDown}
            className={`font-bold mb-6 ${
              variant === 'bold'
                ? shouldShowOverlay()
                  ? 'text-5xl lg:text-7xl text-white text-shadow-strong'
                  : 'text-5xl lg:text-7xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent'
                : variant === 'minimal'
                ? `text-4xl lg:text-6xl ${shouldShowOverlay() ? 'text-white text-shadow-strong' : 'text-gray-900'}`
                : `text-4xl lg:text-6xl ${shouldShowOverlay() ? 'text-white text-shadow-strong' : 'text-gray-900'}`
            }`}
          >
            {data.headline}
          </motion.h1>

          {/* Subheadline */}
          <motion.p
            variants={fadeInUp}
            className={`mb-8 ${
              variant === 'bold'
                ? `text-xl lg:text-2xl max-w-3xl mx-auto leading-relaxed ${shouldShowOverlay() ? 'text-gray-100 text-shadow-strong' : 'text-gray-700'}`
                : variant === 'minimal'
                ? `text-lg lg:text-xl max-w-2xl mx-auto ${shouldShowOverlay() ? 'text-gray-100 text-shadow-strong' : 'text-gray-600'}`
                : `text-xl lg:text-2xl max-w-3xl mx-auto leading-relaxed ${shouldShowOverlay() ? 'text-gray-100 text-shadow-strong' : 'text-gray-600'}`
            }`}
          >
            {data.subheadline}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={staggerItem}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              onClick={handleCTAClick}
              variant={variant === 'bold' ? 'cta' : 'gradient'}
              size="xl"
              className="group"
            >
              {data.ctaText}
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            {variant === 'bold' && (
              <Button
                variant="outline"
                size="xl"
                className="group border-2 hover:bg-white/10"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            )}
          </motion.div>

          {/* Trust indicators */}
          <motion.div
            variants={fadeInUp}
            className="mt-12 pt-8 border-t border-gray-200/50"
          >
            <p className="text-sm text-gray-500 mb-4">Trusted by ambitious founders</p>
            <div className="flex justify-center items-center space-x-8 opacity-60">
              {/* Placeholder for company logos */}
              <div className="h-8 w-24 bg-gray-300 rounded"></div>
              <div className="h-8 w-24 bg-gray-300 rounded"></div>
              <div className="h-8 w-24 bg-gray-300 rounded"></div>
              <div className="h-8 w-24 bg-gray-300 rounded"></div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2"></div>
        </div>
      </motion.div>
    </section>
  );
}
