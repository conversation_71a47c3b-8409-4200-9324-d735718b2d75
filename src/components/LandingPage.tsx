'use client';

import { motion } from 'framer-motion';
import { LandingPageVariant } from '@/types';
import { Hero } from '@/components/sections/Hero';
import { HowItWorks } from '@/components/sections/HowItWorks';
import { Projects } from '@/components/sections/Projects';
import { WhyEquity } from '@/components/sections/WhyEquity';
import { FAQ } from '@/components/sections/FAQ';
import { ContactForm } from '@/components/forms/ContactForm';
import { IdeaSubmissionModal } from '@/components/modals/IdeaSubmissionModal';
import { useIdeaModal } from '@/hooks/useIdeaModal';
import { pageTransition } from '@/lib/animations';
import { submitForm } from '@/lib/form-submission';
import { FormData } from '@/types';

interface LandingPageProps {
  data: LandingPageVariant;
}

export function LandingPage({ data }: LandingPageProps) {
  const { isModalOpen, closeModal } = useIdeaModal();

  const handleFormSubmit = async (formData: FormData) => {
    const result = await submitForm(formData);
    if (!result.success) {
      throw new Error(result.message);
    }
  };

  // Determine component variants based on landing page style
  const getComponentVariants = () => {
    switch (data.id) {
      case 'bold-disruptor':
        return {
          hero: 'bold' as const,
          howItWorks: 'timeline' as const,
          projects: 'grid' as const,
          whyEquity: 'split' as const,
          faq: 'cards' as const,
          form: 'detailed' as const
        };
      case 'founder-focused':
      default:
        return {
          hero: 'default' as const,
          howItWorks: 'cards' as const,
          projects: 'default' as const,
          whyEquity: 'default' as const,
          faq: 'accordion' as const,
          form: 'default' as const
        };
    }
  };

  const variants = getComponentVariants();

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageTransition}
      className="min-h-screen"
    >
      {/* Hero Section */}
      <Hero 
        data={data.hero} 
        variant={variants.hero}
      />

      {/* How It Works Section */}
      <HowItWorks 
        data={data.howItWorks} 
        variant={variants.howItWorks}
      />

      {/* Sample Projects Section */}
      <Projects 
        data={data.projects} 
        variant={variants.projects}
      />

      {/* Why Equity Section */}
      <WhyEquity 
        data={data.whyEquity} 
        variant={variants.whyEquity}
      />

      {/* FAQ Section */}
      <FAQ 
        data={data.faq} 
        variant={variants.faq}
      />

      {/* Contact Form Section */}
      <ContactForm
        title={data.cta.formTitle}
        subtitle={data.cta.subtitle}
        variant={variants.form}
        onSubmit={handleFormSubmit}
      />

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">Helevon</h3>
              <p className="text-gray-300 mb-4 max-w-md">
                We partner with ambitious founders to build world-class MVPs through our equity-based development model.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Twitter</span>
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
              </div>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2">
                <li><a href="https://helevon.com/about" className="text-gray-300 hover:text-white transition-colors">About Us</a></li>
                <li><a href="https://helevon.com/portfolio" className="text-gray-300 hover:text-white transition-colors">Portfolio</a></li>
                <li><a href="https://helevon.com/team" className="text-gray-300 hover:text-white transition-colors">Team</a></li>
                <li><a href="https://helevon.com/careers" className="text-gray-300 hover:text-white transition-colors">Careers</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <ul className="space-y-2 text-gray-300">
                <li><EMAIL></li>
                <li>+1 (555) 123-4567</li>
                <li>San Francisco, CA</li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 Helevon Technologies. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="https://helevon.com/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="https://helevon.com/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>

      {/* Idea Submission Modal */}
      <IdeaSubmissionModal
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </motion.div>
  );
}
