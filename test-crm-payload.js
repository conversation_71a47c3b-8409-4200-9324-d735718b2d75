// Test the CRM payload formatting
const { formatContactFormData, parseFullName } = require('./src/lib/crm-integration.ts');

// Test data similar to what was failing
const testData = {
  name: '<PERSON>',
  email: 'iaman<PERSON><PERSON><EMAIL>',
  phone: undefined, // This was causing the issue
  ideaSummary: 'Test work about how it works',
  budget: undefined,
  timeline: 'ASAP',
  company: undefined
};

console.log('=== TEST CRM PAYLOAD FORMATTING ===');
console.log('Input data:', JSON.stringify(testData, null, 2));

const formattedData = formatContactFormData(testData);
console.log('\nFormatted CRM payload:', JSON.stringify(formattedData, null, 2));

console.log('\n=== EXPECTED RESULT ===');
console.log('firstName: "Daniel"');
console.log('lastName: "Aiyelu"');
console.log('emailAddress: "iaman<PERSON><PERSON><EMAIL>"');
console.log('phoneNumber: "" (empty string, not "NA")');
console.log('description: Contains all form data in readable format');
